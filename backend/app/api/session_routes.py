#!/usr/bin/env python3
"""
Session 管理 API 路由示例
展示如何在 FastAPI 路由中使用 Redis Session 管理
"""

import json
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel
from loguru import logger

from backend.app.db.redis_conn import (
    get_redis_connection,
    get_redis_manager,
    get_app_redis_client,
    redis_manager
)


router = APIRouter(prefix="/api/session", tags=["session"])


class SessionData(BaseModel):
    """Session 数据模型"""
    user_id: str
    email: str
    access_token: str
    expires_at: int


class SessionResponse(BaseModel):
    """Session 响应模型"""
    success: bool
    message: str
    data: Optional[dict] = None


@router.post("/save", response_model=SessionResponse)
async def save_session_global(session_data: SessionData):
    """
    使用全局单例保存 Session
    """
    try:
        async with get_redis_connection() as redis_client:
            session_key = f"session:{session_data.user_id}"
            await redis_client.setex(
                session_key,
                3600,  # 1小时过期
                json.dumps(session_data.dict())
            )
            
        logger.success(f"✅ Session 已保存 (全局方式): {session_key}")
        return SessionResponse(
            success=True,
            message="Session saved successfully using global singleton",
            data={"session_key": session_key}
        )
        
    except Exception as e:
        logger.error(f"❌ 保存 Session 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save session: {str(e)}")


@router.post("/save-appstate", response_model=SessionResponse)
async def save_session_appstate(
    session_data: SessionData,
    request: Request
):
    """
    使用 app.state 保存 Session
    """
    try:
        # 从 app.state 获取 Redis 管理器
        redis_mgr = getattr(request.app.state, 'redis', None) or redis_manager
        
        async with get_redis_connection(redis_mgr) as redis_client:
            session_key = f"session:{session_data.user_id}"
            await redis_client.setex(
                session_key,
                3600,  # 1小时过期
                json.dumps(session_data.dict())
            )
            
        logger.success(f"✅ Session 已保存 (app.state方式): {session_key}")
        return SessionResponse(
            success=True,
            message="Session saved successfully using app.state",
            data={"session_key": session_key}
        )
        
    except Exception as e:
        logger.error(f"❌ 保存 Session 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save session: {str(e)}")


@router.get("/get/{user_id}", response_model=SessionResponse)
async def get_session_global(user_id: str):
    """
    使用全局单例获取 Session
    """
    try:
        async with get_redis_connection() as redis_client:
            session_key = f"session:{user_id}"
            session_data = await redis_client.get(session_key)
            
        if session_data:
            data = json.loads(session_data)
            logger.success(f"✅ Session 已获取 (全局方式): {session_key}")
            return SessionResponse(
                success=True,
                message="Session retrieved successfully using global singleton",
                data=data
            )
        else:
            return SessionResponse(
                success=False,
                message="Session not found"
            )
            
    except Exception as e:
        logger.error(f"❌ 获取 Session 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get session: {str(e)}")


@router.get("/get-appstate/{user_id}", response_model=SessionResponse)
async def get_session_appstate(
    user_id: str,
    request: Request
):
    """
    使用 app.state 获取 Session
    """
    try:
        # 从 app.state 获取 Redis 管理器
        redis_mgr = getattr(request.app.state, 'redis', None) or redis_manager
        
        async with get_redis_connection(redis_mgr) as redis_client:
            session_key = f"session:{user_id}"
            session_data = await redis_client.get(session_key)
            
        if session_data:
            data = json.loads(session_data)
            logger.success(f"✅ Session 已获取 (app.state方式): {session_key}")
            return SessionResponse(
                success=True,
                message="Session retrieved successfully using app.state",
                data=data
            )
        else:
            return SessionResponse(
                success=False,
                message="Session not found"
            )
            
    except Exception as e:
        logger.error(f"❌ 获取 Session 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get session: {str(e)}")


@router.delete("/delete/{user_id}", response_model=SessionResponse)
async def delete_session(user_id: str):
    """
    删除 Session
    """
    try:
        async with get_redis_connection() as redis_client:
            session_key = f"session:{user_id}"
            result = await redis_client.delete(session_key)
            
        if result:
            logger.success(f"✅ Session 已删除: {session_key}")
            return SessionResponse(
                success=True,
                message="Session deleted successfully"
            )
        else:
            return SessionResponse(
                success=False,
                message="Session not found"
            )
            
    except Exception as e:
        logger.error(f"❌ 删除 Session 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete session: {str(e)}")


@router.get("/list", response_model=SessionResponse)
async def list_sessions():
    """
    列出所有 Sessions（使用全局单例）
    """
    try:
        async with get_redis_connection() as redis_client:
            session_keys = await redis_client.keys("session:*")

            sessions = []
            for key in session_keys:
                session_data = await redis_client.get(key)
                if session_data:
                    sessions.append({
                        "key": key,
                        "data": json.loads(session_data)
                    })

        logger.success(f"✅ 列出 {len(sessions)} 个 sessions (全局方式)")
        return SessionResponse(
            success=True,
            message=f"Found {len(sessions)} sessions using global singleton",
            data={"sessions": sessions, "count": len(sessions)}
        )

    except Exception as e:
        logger.error(f"❌ 列出 Sessions 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list sessions: {str(e)}")


@router.get("/list-dependency", response_model=SessionResponse)
async def list_sessions_dependency(redis_mgr = Depends(get_redis_manager)):
    """
    列出所有 Sessions（使用依赖注入获取管理器）
    """
    try:
        async with get_redis_connection(redis_mgr) as redis_client:
            session_keys = await redis_client.keys("session:*")

            sessions = []
            for key in session_keys:
                session_data = await redis_client.get(key)
                if session_data:
                    sessions.append({
                        "key": key,
                        "data": json.loads(session_data)
                    })

        logger.success(f"✅ 列出 {len(sessions)} 个 sessions (依赖注入方式)")
        return SessionResponse(
            success=True,
            message=f"Found {len(sessions)} sessions using dependency injection",
            data={"sessions": sessions, "count": len(sessions)}
        )

    except Exception as e:
        logger.error(f"❌ 列出 Sessions 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list sessions: {str(e)}")


@router.get("/list-direct", response_model=SessionResponse)
async def list_sessions_direct(redis_client = Depends(get_app_redis_client)):
    """
    列出所有 Sessions（直接依赖注入 Redis 客户端）
    """
    try:
        session_keys = await redis_client.keys("session:*")

        sessions = []
        for key in session_keys:
            session_data = await redis_client.get(key)
            if session_data:
                sessions.append({
                    "key": key,
                    "data": json.loads(session_data)
                })

        logger.success(f"✅ 列出 {len(sessions)} 个 sessions (直接注入客户端)")
        return SessionResponse(
            success=True,
            message=f"Found {len(sessions)} sessions using direct client injection",
            data={"sessions": sessions, "count": len(sessions)}
        )

    except Exception as e:
        logger.error(f"❌ 列出 Sessions 失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list sessions: {str(e)}")
