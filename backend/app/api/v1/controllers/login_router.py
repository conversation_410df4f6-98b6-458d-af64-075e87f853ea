from typing import Annotated
from loguru import logger
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Request ,<PERSON>ie, Response
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials ,OAuth2PasswordRequestForm

from backend.app.api.v1.schemas.session_schemas import SessionResponse
from backend.app.repo.session_repository import SessionRepository
from backend.app.services.sb_auth_service import get_auth_service

router = APIRouter(prefix="/api/test", tags=["test"])
security = HTTPBearer()


@router.get("/login", response_model=SessionResponse)
async def login(
        form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
        request: Request,
        session_repo: SessionRepository = Depends(SessionRepository),
        ):
    """
    登录并创建 Session
    """
    try:
        logger.info("🔐 Attempting to sign in...")
        supabase = get_auth_service()

        response = await supabase.sign_in_with_email(email=form_data.username, password=form_data.password)
        logger.info(f"Response: {response}")
        # todo 登录成功就保存
        if response.get("success"):
            success = await session_repo.store_session_to_redis(response)







        # 保存 Session 到 Redis


        logger.success(f"✅ Session 已保存 (全局方式): {session_key}")
        return SessionResponse(
            success=True,
            message="Session saved successfully",
            data={"session_key": session_key}
        )
    except Exception as e:
        logger.error(f"❌ Session 保存失败: {e}")


# @router.get("/get_access_token")
# async def get_access_token(*,
#             request: Request,
#             credentials: HTTPAuthorizationCredentials = Depends(security),
#             session_repo: SessionRepository = Depends(SessionRepository),
#             refresh_token_from_cookie: str = Cookie( None, alias="refresh_token", include_in_schema=False),
#     ):
#         """
#         获取 Access Token
#         :param request:
#         :param credentials:
#         :param session_repo:
#         :param refresh_token_from_cookie:
#         :return:
#         """
#         access_token = credentials.credentials if credentials and credentials.scheme == "Bearer" else None
#         scheme = credentials.scheme
#         # 添加详细的调试日志
#         logger.debug(f"Authorization header: {credentials}")
#         logger.debug(f"Refresh token cookie length: {len(refresh_token_from_cookie) if refresh_token_from_cookie else 0}")
#
