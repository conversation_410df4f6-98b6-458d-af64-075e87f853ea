import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

import redis.asyncio as redis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from loguru import logger
from fastapi import Request

from backend.app.config.env import redis_settings
from backend.app.core.utils import AsyncSingletonMeta


class RedisManager(metaclass=AsyncSingletonMeta):
    """Redis 连接管理器 - 使用异步单例元类"""

    def __init__(self):
        # 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.pool: Optional[redis.ConnectionPool] = None
        self.client: Optional[redis.Redis] = None
        self._initialized = False
        self._init_lock = asyncio.Lock()
        self.redis_url = f"redis://{redis_settings.redis_username}:{redis_settings.redis_password}@{redis_settings.redis_host}:{redis_settings.redis_port}/{redis_settings.redis_db}"

    async def initialize(self):
        """异步初始化 Redis 连接池和客户端"""
        if self._initialized:
            return

        async with self._init_lock:
            if self._initialized:
                return

            try:
                self.pool = redis.ConnectionPool.from_url(
                    self.redis_url,
                    decode_responses=redis_settings.redis_decode_responses,
                    max_connections=redis_settings.redis_max_connections,
                    socket_connect_timeout=redis_settings.redis_socket_connect_timeout,
                    socket_timeout=redis_settings.redis_socket_timeout,
                    retry_on_timeout=redis_settings.redis_retry_on_timeout,
                    health_check_interval=redis_settings.redis_health_check_interval,
                )
                self.client = redis.Redis(connection_pool=self.pool)
                await self.client.ping()  # 测试连接
                self._initialized = True
                logger.info(f"✅ Redis连接池初始化成功: {redis_settings.redis_host}:{redis_settings.redis_port}")

            except Exception as e:
                logger.error(f"❌ Redis初始化失败: {e}")
                self.pool = None
                self.client = None
                raise ConnectionError(f"Redis初始化失败: {e}") from e

    async def get_client(self) -> redis.Redis:
        """获取 Redis 客户端，支持自动重连"""
        if not self._initialized:
            await self.initialize()

        if self.client is None:
            raise ConnectionError("Redis客户端未初始化")

        try:
            await self.client.ping()
            return self.client
        except (ConnectionError, TimeoutError, RedisError) as e:
            logger.warning(f"⚠️ Redis连接失效，尝试重新初始化: {e}")
            self._initialized = False
            await self.initialize()
            return self.client

    async def close(self):
        """关闭 Redis 连接"""
        if self.client:
            await self.client.aclose()
            self.client = None
        if self.pool:
            await self.pool.aclose()
            self.pool = None
        self._initialized = False
        logger.info("🔒 Redis连接已关闭")

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if self.client:
                await self.client.ping()
                return True
        except Exception as e:
            logger.warning(f"⚠️ Redis健康检查失败: {e}")
        return False


# 全局单例实例
redis_manager = RedisManager()


@asynccontextmanager
async def get_redis_connection(manager: Optional['RedisManager'] = None) -> AsyncGenerator[redis.Redis, None]:
    """
    Redis 连接依赖 - 用于 FastAPI 依赖注入
    自动管理连接的获取，支持自动重连

    Args:
        manager: 可选的 Redis 管理器实例，如果不提供则使用全局单例
    """
    try:
        # 使用提供的管理器或全局单例
        mgr = manager or redis_manager
        client = await mgr.get_client()
        yield client
    except Exception as e:
        logger.error(f"❌ 获取Redis连接失败: {e}")
        raise ConnectionError(f"获取Redis连接失败: {e}") from e


# 为了向后兼容，提供一个 FastAPI 依赖函数
async def get_redis_from_app_state(request) -> 'RedisManager':
    """
    从 FastAPI app.state 获取 Redis 管理器的依赖函数

    Usage:
        @app.get("/api/data")
        async def get_data(redis_mgr: RedisManager = Depends(get_redis_from_app_state)):
            async with get_redis_connection(redis_mgr) as redis_client:
                # 使用连接
    """

    if isinstance(request, Request):
        return getattr(request.app.state, 'redis', None) or redis_manager
    return redis_manager
