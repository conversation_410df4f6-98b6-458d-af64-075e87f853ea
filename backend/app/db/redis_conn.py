import redis.asyncio as redis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from loguru import logger
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from backend.app.config.env import redis_settings


redis_url = f"redis://{redis_settings.redis_username}:{redis_settings.redis_password}@{redis_settings.redis_host}:{redis_settings.redis_port}/{redis_settings.redis_db}"

# 配置 Redis 连接池和客户端
def create_redis_pool():
    """创建 Redis 连接池和客户端"""
    try:
        pool = redis.ConnectionPool.from_url(
            redis_url,
            decode_responses=redis_settings.redis_decode_responses,
            max_connections=redis_settings.redis_max_connections,
            socket_connect_timeout=redis_settings.redis_socket_connect_timeout,
            socket_timeout=redis_settings.redis_socket_timeout,
            retry_on_timeout=redis_settings.redis_retry_on_timeout,
            health_check_interval=redis_settings.redis_health_check_interval,
        )
        client = redis.Redis(connection_pool=pool)
        logger.info(f"Redis连接池和客户端创建成功: {redis_settings.redis_host}:{redis_settings.redis_port}")
        return pool, client
    except Exception as e:
        logger.error(f"创建Redis连接池和客户端失败: {e}")
        return None, None

# 全局连接池和客户端
redis_pool, redis_client = create_redis_pool()


@asynccontextmanager
async def get_redis_connection() -> AsyncGenerator[redis.Redis, None]:
    """
    Redis 连接依赖 - 用于 FastAPI 依赖注入
    自动管理连接的创建和关闭
    """
    global redis_client, redis_pool

    if redis_client is None:
        logger.error("Redis客户端未初始化")
        raise ConnectionError("Redis客户端未初始化")

    try:
        # 检查连接是否成功
        await redis_client.ping()
    except (ConnectionError, TimeoutError, RedisError) as e:
        logger.warning(f"Redis连接失效，尝试重新连接: {e}")
        # 尝试重新创建连接池和客户端
        redis_pool, redis_client = create_redis_pool()
        if redis_client is None:
            logger.error("重新创建Redis客户端失败")
            raise ConnectionError("重新创建Redis客户端失败")
        try:
            await redis_client.ping()
        except Exception as e2:
            logger.error(f"重新连接Redis失败: {e2}")
            raise ConnectionError("重新连接Redis失败") from e2

    yield redis_client
