import asyncio
import redis.asyncio as redis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from loguru import logger
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
from backend.app.config.env import redis_settings


class RedisManager:
    """Redis 连接管理器 - 单例模式"""
    _instance: Optional['RedisManager'] = None
    _lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self.pool: Optional[redis.ConnectionPool] = None
        self.client: Optional[redis.Redis] = None
        self._initialized = False
        self.redis_url = f"redis://{redis_settings.redis_username}:{redis_settings.redis_password}@{redis_settings.redis_host}:{redis_settings.redis_port}/{redis_settings.redis_db}"

    async def initialize(self):
        """异步初始化 Redis 连接池和客户端"""
        if self._initialized:
            return

        async with self._lock:
            if self._initialized:
                return

            try:
                self.pool = redis.ConnectionPool.from_url(
                    self.redis_url,
                    decode_responses=redis_settings.redis_decode_responses,
                    max_connections=redis_settings.redis_max_connections,
                    socket_connect_timeout=redis_settings.redis_socket_connect_timeout,
                    socket_timeout=redis_settings.redis_socket_timeout,
                    retry_on_timeout=redis_settings.redis_retry_on_timeout,
                    health_check_interval=redis_settings.redis_health_check_interval,
                )
                self.client = redis.Redis(connection_pool=self.pool)
                await self.client.ping()  # 测试连接
                self._initialized = True
                logger.info(f"✅ Redis连接池初始化成功: {redis_settings.redis_host}:{redis_settings.redis_port}")

            except Exception as e:
                logger.error(f"❌ Redis初始化失败: {e}")
                self.pool = None
                self.client = None
                raise ConnectionError(f"Redis初始化失败: {e}") from e

    async def get_client(self) -> redis.Redis:
        """获取 Redis 客户端，支持自动重连"""
        if not self._initialized:
            await self.initialize()

        if self.client is None:
            raise ConnectionError("Redis客户端未初始化")

        try:
            await self.client.ping()
            return self.client
        except (ConnectionError, TimeoutError, RedisError) as e:
            logger.warning(f"⚠️ Redis连接失效，尝试重新初始化: {e}")
            self._initialized = False
            await self.initialize()
            return self.client

    async def close(self):
        """关闭 Redis 连接"""
        if self.client:
            await self.client.aclose()
            self.client = None
        if self.pool:
            await self.pool.aclose()
            self.pool = None
        self._initialized = False
        logger.info("🔒 Redis连接已关闭")

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if self.client:
                await self.client.ping()
                return True
        except Exception as e:
            logger.warning(f"⚠️ Redis健康检查失败: {e}")
        return False


# 全局单例实例
redis_manager = RedisManager()


@asynccontextmanager
async def get_redis_connection() -> AsyncGenerator[redis.Redis, None]:
    """
    Redis 连接依赖 - 用于 FastAPI 依赖注入
    自动管理连接的获取，支持自动重连
    """
    try:
        client = await redis_manager.get_client()
        yield client
    except Exception as e:
        logger.error(f"❌ 获取Redis连接失败: {e}")
        raise ConnectionError(f"获取Redis连接失败: {e}") from e
