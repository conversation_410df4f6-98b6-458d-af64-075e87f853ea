import redis.asyncio as redis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from loguru import logger
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from backend.app.config.env import redis_settings



# 配置 Redis 连接池
def create_redis_pool():
    """创建 Redis 连接池"""
    try:
        pool_config = {
            'host': redis_settings.redis_host,
            'port': redis_settings.redis_port,
            'db': redis_settings.redis_db,
            'password': redis_settings.redis_password,
            'username': redis_settings.redis_username,
            'decode_responses': redis_settings.redis_decode_responses,
            'max_connections': redis_settings.redis_max_connections,
            'socket_connect_timeout': redis_settings.redis_socket_connect_timeout,
            'socket_timeout': redis_settings.redis_socket_timeout,
            'retry_on_timeout': redis_settings.redis_retry_on_timeout,
            'health_check_interval': redis_settings.redis_health_check_interval,
        }
        # 移除 None 值
        pool_config = {k: v for k, v in pool_config.items() if v is not None}

        pool = redis.ConnectionPool(**pool_config)
        logger.info(f"Redis连接池创建成功: {redis_settings.redis_host}:{redis_settings.redis_port}")
        return pool
    except Exception as e:
        logger.error(f"创建Redis连接池失败: {e}")
        return None

# 全局连接池
redis_pool = create_redis_pool()





@asynccontextmanager
async def get_redis_connection() -> AsyncGenerator[redis.Redis, None]:
    """
    Redis 连接依赖 - 用于 FastAPI 依赖注入
    自动管理连接的创建和关闭
    """
    if redis_pool is None:
        logger.error("Redis连接池未初始化")
        raise ConnectionError("Redis连接池未初始化")

    redis_client = None

    try:
        redis_client = redis.Redis(connection_pool=redis_pool)

        # 检查连接是否成功
        await redis_client.ping()

        yield redis_client

    except (ConnectionError, TimeoutError, RedisError) as e:
        # Redis 内部已有详细的错误信息，直接抛出
        raise





