# app/models/base_models.py

"""
数据模型基础类模块

定义 SQLAlchemy 的基础模型类和通用混入类。
包含时间戳混入类和抽象基础模型类，为所有数据模型提供统一的基础结构。
"""
import json
from datetime import datetime

from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import Integer,  DateTime ,MetaData
from sqlalchemy import TypeDecorator, Text

class Base(DeclarativeBase):
    pass


class TimeStampedMixin:
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class DBModel(TimeStampedMixin, Base):
    __abstract__ = True
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)

# 添加 Schema 基类
class AuthBase(DBModel):
    __abstract__ = True
    metadata = MetaData(schema="auth")