
# app/models/user_models.py

from datetime import datetime


from sqlalchemy import String, Text, Integer, Enum, ForeignKey, Column, Table , DateTime ,Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import JSONB


from backend.app.models.base_models import AuthBase
from backend.app.config.enums import RoleType ,PermissionType


# 定义角色类型枚举


# 使用正确的 Column 定义关联表
associate_roles_permissions = Table(
    'associate_roles_permissions',
    AuthBase.metadata,
    Column('role_name', Enum(
        RoleType,
        values_callable=lambda enum_cls: [e.value for e in enum_cls],
        native_enum=False
    ), ForeignKey('roles.name'), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id'), primary_key=True)
)

associate_users_roles = Table(
    'associate_users_roles',
    AuthBase.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    <PERSON>umn('role_name', Enum(
        RoleType,
        values_callable=lambda enum_cls: [e.value for e in enum_cls],
        native_enum=False
    ), ForeignKey('roles.name'), primary_key=True)
)

# 令牌-API权限范围关联表
associate_token_api_scopes = Table(
    'associate_token_api_scopes',
    AuthBase.metadata,
    Column('token_id', Integer, ForeignKey('user_tokens.id'), primary_key=True),
    Column('api_scope_id', Integer, ForeignKey('api_scopes.id'), primary_key=True)
)


class User(AuthBase):
    """用户数据模型"""

    __tablename__ = 'users'

    # 用户唯一标识
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)

    # 用户信息
    username: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True)
    password: Mapped[str] = mapped_column(String(255), nullable=False)

    # role_name: Mapped[RoleType] = mapped_column(Enum(RoleType), ForeignKey('roles.name'), nullable=False)

    # # 使用role.name作为外键
    # role_name: Mapped[RoleType] = mapped_column(
    #     Enum(
    #         RoleType,
    #         values_callable=lambda enum_cls: [e.value for e in enum_cls],
    #         native_enum=False,
    #     ),
    #     ForeignKey('roles.name'),
    #     nullable=False
    # )
    # 添加与角色的关系
    roles: Mapped[list['Role']] = relationship(
        secondary=associate_users_roles,
        back_populates='users',
        lazy='selectin'  # 使用 selectin 加载策略，避免懒加载问题
    )

    # 添加与令牌的关系
    tokens: Mapped[list["UserApiToken"]] = relationship(
        "UserApiToken",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy='selectin'  # 使用 selectin 加载策略
    )

    def __str__(self):
        """返回用户的字符串表示"""
        return f"User(id={self.id}, username={self.username})"


    def __repr__(self):
        """返回用户的字符串表示"""
        role_names = [role.name.value if hasattr(role.name, 'value') else str(role.name) for role in self.roles] if self.roles else []
        return f"User(id={self.id}, username={self.username}, roles={role_names})"



class Role(AuthBase):
    """角色数据模型"""
    __tablename__ = 'roles'
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    # name: Mapped[RoleType] = mapped_column(Enum(RoleType), nullable=False, unique=True, index=True)

    # values_callable 告诉 SQLAlchemy 使用枚举的值列表作为数据库中允许的枚举值。
    name: Mapped[RoleType] = mapped_column(
        Enum(
            RoleType,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],
            native_enum=False,  # 根据需要决定是否使用数据库原生枚举类型
        ),
        nullable=False,
        unique=True,
        index=True
    )

    description: Mapped[str] = mapped_column(Text, nullable=True)

    # 其他字段和关系
    users: Mapped[list["User"]] = relationship(
        secondary=associate_users_roles,
        back_populates='roles',
        lazy='selectin'  # 使用 selectin 加载策略
    )
    permissions: Mapped[list["Permission"]] = relationship(
        secondary=associate_roles_permissions,
        back_populates='roles',
        lazy='selectin'  # 使用 selectin 加载策略
    )

    def __str__(self):
        """返回角色的字符串表示"""
        return f"Role(id={self.id}, name={self.name}, description={self.description} )"

    def __repr__(self):
        """返回角色的字符串表示"""
        return f"Role(id={self.id}, name={self.name}, description={self.description} )"

class Permission(AuthBase):
    """权限数据模型"""
    __tablename__ = 'permissions'
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    name: Mapped[PermissionType] = mapped_column(
        Enum(
            PermissionType,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],
            native_enum=False
        ),
        nullable=False,
        unique=True,
        index=True
    )

    description: Mapped[str] = mapped_column(Text, nullable=True)

    # 其他字段和关系
    roles: Mapped[list["Role"]] = relationship(
        secondary=associate_roles_permissions,
        back_populates='permissions',
        lazy='selectin'  # 使用 selectin 加载策略
    )

    def __str__(self):
        """返回权限的字符串表示"""
        return f"Permission(id={self.id}, name={self.name}, description={self.description})"

    def __repr__(self):
        """返回权限的字符串表示"""
        return f"Permission(id={self.id}, name={self.name}, description={self.description})"


# API权限范围配置表
class ApiScopes(AuthBase):
    """
    API权限范围配置模型

    定义每个API端点所需的权限范围（scopes），用于访问控制
    """
    __tablename__ = "api_scopes"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    api_path: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True, comment="API路径")
    description: Mapped[str] = mapped_column(Text, nullable=True, comment="API描述")
    required_scopes: Mapped[list[str]] = mapped_column(JSONB, default=list, nullable=False, comment="所需权限范围")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, comment="是否启用")
    category: Mapped[str] = mapped_column(String(50), nullable=False, default="default", comment="API分类")

    # 添加与令牌的反向关系
    tokens: Mapped[list["UserApiToken"]] = relationship(
        "UserApiToken",
        secondary=associate_token_api_scopes,
        back_populates="api_scopes"
    )




class UserApiToken(AuthBase):
    """用户令牌模型，用于跟踪用户的活跃令牌"""
    __tablename__ = 'user_tokens'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, autoincrement=True)
    token_name: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True)
    expires_at: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey('users.id'), nullable=False)
    token: Mapped[str] = mapped_column(Text, nullable=False, unique=True, index=True)
    permissions: Mapped[list[str]] = mapped_column(JSONB, nullable=True)
    api_paths: Mapped[list[str]] = mapped_column(JSONB, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)


    # 添加与用户的关联
    user: Mapped["User"] = relationship("User", back_populates="tokens")

    # 关联的API权限范围
    api_scopes: Mapped[list["ApiScopes"]] = relationship(
        "ApiScopes",
        secondary=associate_token_api_scopes,
        back_populates="tokens"
    )




