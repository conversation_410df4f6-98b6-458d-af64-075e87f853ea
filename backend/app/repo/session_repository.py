

from datetime import datetime, timezone
import redis.asyncio as redis
from typing import Optional
from loguru import logger
from fastapi import Request


class SessionRepository:
    """Refresh Token Redis 仓储类"""

    def __init__(self, request: Request):
        """
        初始化仓储，从应用状态获取Redis客户端

        Args:
            request: FastAPI请求对象
        """
        self.redis_client: redis.Redis = request.app.state.redis  # type: ignore
        if not self.redis_client:
            logger.warning("Redis client not available, session functionality will be disabled")

    