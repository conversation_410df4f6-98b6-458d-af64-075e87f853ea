
import json
from datetime import datetime, timezone
import redis.asyncio as redis
from typing import Optional
from loguru import logger
from fastapi import APIRouter, Depends, HTTPException, Request

from backend.app.db.redis_conn import (
    get_redis_connection,
    get_redis_manager,
    get_app_redis_client,
    redis_manager
)
from backend.app.api.v1.schemas.session_schemas import SessionResponse

class SessionRepository:
    """Refresh Token Redis 仓储类"""

    def __init__(self, request: Request):
        """
        初始化仓储，从应用状态获取Redis客户端

        Args:
            request: FastAPI请求对象
        """
        self.redis_manager: RedisManager = request.app.state.redis  # type: ignore
        if not self.redis_manager:
            logger.warning("Redis not available, session functionality will be disabled")

    async def store_session_to_redis(self, session_data: dict) -> SessionResponse:
        """
        在 Redis 中存储 session 及相关信息

        Args:
            username: 用户名
            payload: JWT payload

        Returns:
            bool: 存储是否成功
        """
        try:
            async with get_redis_connection(self.redis_manager) as redis_client:
                session_key = f"session:{session_data.user.id}"
                logger.debug(f"Storing session to Redis: {session_key}")
                await redis_client.setex(
                    session_key,
                    3600,  # 1小时过期
                    json.dumps(session_data)
                )
            logger.success(f"✅ Session 已保存 (app.state方式): {session_key}")
            return SessionResponse(
                success=True,
                message="Session saved successfully using global singleton",
                data={"session_key": session_key}
            )
        except Exception as e:
            logger.error(f"❌ 保存 Session 失败: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to save session: {str(e)}")


    async def get_session_from_redis(self, session_key: str) -> Optional[dict]:
        """
        从 Redis 中获取 session 及相关信息

        Args:
            session_id: session ID

        Returns:
            dict: session 信息
        """
        try:
            async with get_redis_connection(self.redis_manager) as redis_client:
                session_key = f"session:{session_key}"
                return await redis_client.get(session_key)
        except Exception as e:
            logger.error(f"Error getting session from Redis: {e}")
            return None

    #
    # async def store_refresh_token(self, username: str, payload: dict) -> bool:
    #     """
    #     在 Redis 中存储 refresh token 及相关信息
    #
    #     Args:
    #         username: 用户名
    #         payload: JWT payload
    #
    #     Returns:
    #         bool: 存储是否成功
    #     """
    #     if not self.redis_client:
    #         logger.warning("Redis client not available, cannot store refresh token")
    #         return False
    #
    #     try:
    #
    #         sub = payload.get("sub")
    #         if not sub:
    #             raise ValueError(
    #                 f"Failed to store refresh token for {username}: sub is None"
    #             )
    #
    #         exp = payload.get("exp")
    #         expire_seconds = exp - datetime.now(timezone.utc).timestamp()
    #
    #         # 检查token是否已经过期
    #         if expire_seconds <= 0:
    #             logger.warning("Token already expired")
    #             return False
    #
    #         # 使用jti作为key（如果有的话），否则使用username
    #         key = f"refresh_token:{payload.get('token_id')}"
    #
    #         # 存储到Redis
    #         result = await self.redis_client.setex(
    #             key,
    #             int(expire_seconds),
    #             sub
    #         )
    #
    #         logger.info(
    #             f"Refresh token stored - key: {key}, sub: {sub}, expires in {expire_seconds} seconds")
    #         return bool(result)
    #
    #     except Exception as e:
    #         logger.error(f"Failed to store refresh token for {username}: {e}")
    #         return False