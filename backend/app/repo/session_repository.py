

from datetime import datetime, timezone
import redis.asyncio as redis
from typing import Optional
from loguru import logger
from fastapi import Request


class SessionRepository:
    """Refresh Token Redis 仓储类"""

    def __init__(self, request: Request):
        """
        初始化仓储，从应用状态获取Redis客户端

        Args:
            request: FastAPI请求对象
        """
        self.redis_client: redis.Redis = request.app.state.redis  # type: ignore
        if not self.redis_client:
            logger.warning("Redis client not available, session functionality will be disabled")



    async def store_refresh_token(self, username: str, payload: dict) -> bool:
        """
        在 Redis 中存储 refresh token 及相关信息

        Args:
            username: 用户名
            payload: JWT payload

        Returns:
            bool: 存储是否成功
        """
        if not self.redis_client:
            logger.warning("Redis client not available, cannot store refresh token")
            return False

        try:

            sub = payload.get("sub")
            if not sub:
                raise ValueError(
                    f"Failed to store refresh token for {username}: sub is None"
                )

            exp = payload.get("exp")
            expire_seconds = exp - datetime.now(timezone.utc).timestamp()

            # 检查token是否已经过期
            if expire_seconds <= 0:
                logger.warning("Token already expired")
                return False

            # 使用jti作为key（如果有的话），否则使用username
            key = f"refresh_token:{payload.get('token_id')}"

            # 存储到Redis
            result = await self.redis_client.setex(
                key,
                int(expire_seconds),
                sub
            )

            logger.info(
                f"Refresh token stored - key: {key}, sub: {sub}, expires in {expire_seconds} seconds")
            return bool(result)

        except Exception as e:
            logger.error(f"Failed to store refresh token for {username}: {e}")
            return False