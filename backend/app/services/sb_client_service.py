"""
Supabase客户端服务

提供Supabase数据库和认证服务的统一接口
"""


from loguru import logger
from supabase import create_client, Client


from backend.app.config.env import supabase_settings


class SupabaseClient:
    """Supabase客户端封装类"""

    def __init__(self):
        """初始化Supabase客户端"""
        self.url = supabase_settings.supabase_url
        self.anon_key = supabase_settings.supabase_anon_key
        self.service_role_key = supabase_settings.supabase_service_role_key

        if not self.url or not self.anon_key:
            raise ValueError("Supabase URL and ANON_KEY must be provided")

        # 创建客户端实例
        self.client: Client = create_client(self.url, self.anon_key)
        self.admin_client: Client = create_client(self.url, self.service_role_key) if self.service_role_key else None

        logger.info("Supabase client initialized successfully")
    
    def get_client(self, admin: bool = False) -> Client:
        """
        获取Supabase客户端
        
        Args:
            admin: 是否使用管理员权限客户端
            
        Returns:
            Supabase客户端实例
        """
        if admin and self.admin_client:
            return self.admin_client
        return self.client
    



# 创建全局实例（推荐使用方式）
# 这不是严格的单例模式，但在实际使用中已经足够
# 如果需要多个实例（比如连接不同的Supabase项目），可以直接实例化类
supabase_client = SupabaseClient()


def get_supabase_client() -> SupabaseClient:
    """
    获取Supabase客户端实例

    这是推荐的获取客户端的方式，便于依赖注入和测试

    Returns:
        SupabaseClient: Supabase客户端实例
    """
    return supabase_client

