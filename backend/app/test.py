import asyncio

from typing import Optional
from loguru import logger
from supabase import create_client, Client
from fastapi import Depends, HTTPException, status,  <PERSON>ie
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,HTTP<PERSON>earer, HTTPAuthorizationCredentials

from backend.app.config.env import supabase_settings


security = HTTPBearer()

url = supabase_settings.supabase_url
anon_key = supabase_settings.supabase_anon_key
service_key = supabase_settings.supabase_service_role_key

# 使用普通客户端进行认证
supa: Client = create_client(url, anon_key)
# 使用管理员客户端进行用户管理
admin_supa: Client = create_client(url, service_key)




async def create_and_confirm_user():
    """创建用户并自动确认"""
    try:
        # 使用管理员权限创建用户并自动确认
        response = admin_supa.auth.admin.create_user({
            "email": "<EMAIL>",
            "password": "test123",
            "user_metadata": {"name": "Yoda"},
            "email_confirm": True  # 自动确认邮箱
        })

        if response.user:
            logger.success(f"✅ User created successfully: {response.user.email}")
            return True
        else:
            logger.error("❌ Failed to create user")
            return False

    except Exception as e:
        if "already been registered" in str(e):
            logger.info("ℹ️ User already exists, trying to confirm...")
            # 如果用户已存在，尝试确认用户
            try:
                # 获取用户信息
                users = admin_supa.auth.admin.list_users()
                target_user = None
                for user in users:
                    if user.email == "<EMAIL>":
                        target_user = user
                        break

                if target_user and not target_user.email_confirmed_at:
                    # 确认用户邮箱
                    admin_supa.auth.admin.update_user_by_id(
                        target_user.id,
                        {"email_confirm": True}
                    )
                    logger.success("✅ User email confirmed")
                    return True
                elif target_user:
                    logger.success("✅ User already confirmed")
                    return True

            except Exception as confirm_error:
                logger.error(f"❌ Error confirming user: {confirm_error}")
                return False
        else:
            logger.error(f"❌ Error creating user: {e}")
            return False


async def sign_in():
    """用户登录"""
    try:
        # 首先确保用户存在并已确认
        user_ready = await create_and_confirm_user()
        if not user_ready:
            logger.error("❌ User setup failed")
            return None

        # 尝试登录
        logger.info("🔐 Attempting to sign in...")
        res = supa.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })

        if res.user and res.session:
            logger.success(f"✅ Sign in successful!")
            logger.info(f"User ID: {res.user.id}")
            logger.info(f"Email: {res.user.email}")
            logger.info(f"Access Token: {res.session.access_token[:50]}...")
            return res.session.access_token
        else:
            logger.error("❌ Sign in failed - no user or session returned")
            return None

    except Exception as e:
        logger.error(f"❌ Sign in error: {e}")
        return None

async def get_session():
    try:
        # 获取用户会话
        session = supa.auth.get_session()
        if session:
            logger.success(f"✅ Session retrieved successfully: {session.access_token[:50]}...")
            return session.access_token
        else:
            logger.error("❌ Failed to retrieve session")
            return None

    except Exception as e:
        logger.error(f"❌ Error retrieving session: {e}")
        return None


async def get_user():
    try:
        logger.info("🔍 Attempting to get user...")
        # 获取用户信息
        user = supa.auth.get_user()
        if user:
            logger.success(f"✅ User retrieved successfully: {user.email}")
            return user
        else:
            logger.error("❌ Failed to retrieve user")
            return None

    except Exception as e:
        logger.error(f"❌ Error retrieving user: {e}")
        return None

async def get_claims():
    try:
        logger.info("🔍 Attempting to get claims...")
        # 获取用户信息
        user = supa.auth.get_claims()
        if user:
            logger.success(f"✅ User retrieved successfully: {user.email}")
            return user
        else:
            logger.error("❌ Failed to retrieve user")
            return None

    except Exception as e:
        logger.error(f"❌ Error retrieving user: {e}")
        return None

async def authenticate_token(*,
                             credentials: HTTPAuthorizationCredentials = Depends(security),
                             ):
    token = credentials.credentials if credentials and credentials.scheme == "Bearer" else None
    logger.info(f"🔍 Attempting to authenticate token: {token[:50]}...")

async def test_authenticate_token():
    """测试版本的 authenticate_token 函数"""

    # 模拟一个 Bearer token
    mock_credentials = HTTPAuthorizationCredentials(
        scheme="Bearer",
        credentials="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token_here"
    )

    # 手动调用函数逻辑
    token = mock_credentials.credentials if mock_credentials and mock_credentials.scheme == "Bearer" else None
    logger.info(f"🔍 Attempting to authenticate token: {token[:50]}...")

    return token

if __name__ == "__main__":
    asyncio.run(test_authenticate_token())
