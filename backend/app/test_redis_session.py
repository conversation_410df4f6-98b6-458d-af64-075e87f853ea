#!/usr/bin/env python3
"""
Redis Session 管理测试文件
测试优化后的 Redis 单例管理器和 Session 功能
"""

import asyncio
import json
from typing import Optional
from loguru import logger
from supabase import create_client, Client
from fastapi.security import HTTPAuthorizationCredentials

from backend.app.config.env import supabase_settings
from backend.app.db.redis_conn import redis_manager, get_redis_connection


# Supabase 客户端
url = supabase_settings.supabase_url
anon_key = supabase_settings.supabase_anon_key
service_key = supabase_settings.supabase_service_role_key

supa: Client = create_client(url, anon_key)
admin_supa: Client = create_client(url, service_key)


class SessionManager:
    """Session 管理器，使用 Redis 存储"""
    
    @staticmethod
    async def save_session_to_redis(user_id: str, session_data: dict, expire_seconds: int = 3600):
        """将 session 保存到 Redis"""
        try:
            async with get_redis_connection() as redis_client:
                session_key = f"session:{user_id}"
                await redis_client.setex(
                    session_key, 
                    expire_seconds,  # 1小时过期
                    json.dumps(session_data)
                )
                logger.success(f"✅ Session 已保存到 Redis: {session_key}")
                return True
        except Exception as e:
            logger.error(f"❌ 保存 Session 到 Redis 失败: {e}")
            return False

    @staticmethod
    async def get_session_from_redis(user_id: str) -> Optional[dict]:
        """从 Redis 获取 session"""
        try:
            async with get_redis_connection() as redis_client:
                session_key = f"session:{user_id}"
                session_data = await redis_client.get(session_key)
                if session_data:
                    logger.success(f"✅ 从 Redis 获取 Session: {session_key}")
                    return json.loads(session_data)
                else:
                    logger.info(f"📭 Redis 中没有找到 Session: {session_key}")
                    return None
        except Exception as e:
            logger.error(f"❌ 从 Redis 获取 Session 失败: {e}")
            return None

    @staticmethod
    async def delete_session_from_redis(user_id: str):
        """从 Redis 删除 session"""
        try:
            async with get_redis_connection() as redis_client:
                session_key = f"session:{user_id}"
                result = await redis_client.delete(session_key)
                if result:
                    logger.success(f"✅ 已从 Redis 删除 Session: {session_key}")
                else:
                    logger.info(f"📭 Redis 中没有找到要删除的 Session: {session_key}")
                return result
        except Exception as e:
            logger.error(f"❌ 从 Redis 删除 Session 失败: {e}")
            return False

    @staticmethod
    async def list_all_sessions():
        """列出所有 sessions"""
        try:
            async with get_redis_connection() as redis_client:
                session_keys = await redis_client.keys("session:*")
                logger.info(f"📋 找到 {len(session_keys)} 个 sessions")
                
                sessions = []
                for key in session_keys:
                    session_data = await redis_client.get(key)
                    if session_data:
                        sessions.append({
                            "key": key,
                            "data": json.loads(session_data)
                        })
                
                return sessions
        except Exception as e:
            logger.error(f"❌ 列出 sessions 失败: {e}")
            return []


async def test_redis_manager():
    """测试 Redis 管理器"""
    logger.info("🧪 开始测试 Redis 管理器...")
    
    try:
        # 1. 初始化 Redis 管理器
        await redis_manager.initialize()
        logger.success("✅ Redis 管理器初始化成功")
        
        # 2. 健康检查
        is_healthy = await redis_manager.health_check()
        logger.info(f"🏥 Redis 健康检查: {'✅ 健康' if is_healthy else '❌ 不健康'}")
        
        # 3. 测试基本连接
        async with get_redis_connection() as redis_client:
            await redis_client.set("test_key", "test_value")
            value = await redis_client.get("test_key")
            logger.success(f"✅ Redis 基本操作测试成功: {value}")
            await redis_client.delete("test_key")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis 管理器测试失败: {e}")
        return False


async def test_supabase_login_and_session():
    """测试 Supabase 登录和 Session 管理"""
    logger.info("🧪 开始测试 Supabase 登录和 Session 管理...")
    
    try:
        # 1. 尝试登录
        response = supa.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if response.session and response.user:
            logger.success(f"✅ Supabase 登录成功: {response.user.email}")
            
            # 2. 保存 session 到 Redis
            session_data = {
                "access_token": response.session.access_token,
                "refresh_token": response.session.refresh_token,
                "expires_at": response.session.expires_at,
                "user_id": response.user.id,
                "user_email": response.user.email
            }
            
            await SessionManager.save_session_to_redis(response.user.id, session_data)
            
            # 3. 从 Redis 获取 session
            retrieved_session = await SessionManager.get_session_from_redis(response.user.id)
            if retrieved_session:
                logger.success(f"✅ 从 Redis 成功获取 Session: {retrieved_session['user_email']}")
            
            # 4. 验证 token
            user_response = supa.auth.get_user(response.session.access_token)
            if user_response.user:
                logger.success(f"✅ Token 验证成功: {user_response.user.email}")
            
            # 5. 列出所有 sessions
            all_sessions = await SessionManager.list_all_sessions()
            logger.info(f"📋 当前 Redis 中有 {len(all_sessions)} 个 sessions")
            
            return response.user.id
            
        else:
            logger.error("❌ Supabase 登录失败")
            return None
            
    except Exception as e:
        logger.error(f"❌ Supabase 登录和 Session 测试失败: {e}")
        return None


async def test_session_cleanup(user_id: str):
    """测试 Session 清理"""
    logger.info("🧪 开始测试 Session 清理...")
    
    try:
        # 删除 session
        result = await SessionManager.delete_session_from_redis(user_id)
        if result:
            logger.success("✅ Session 清理成功")
        
        # 验证删除
        session = await SessionManager.get_session_from_redis(user_id)
        if session is None:
            logger.success("✅ Session 已成功删除")
        else:
            logger.warning("⚠️ Session 删除可能未成功")
            
    except Exception as e:
        logger.error(f"❌ Session 清理测试失败: {e}")


async def main():
    """主测试函数"""
    logger.info("🚀 开始 Redis Session 管理测试...")
    
    try:
        # 1. 测试 Redis 管理器
        redis_ok = await test_redis_manager()
        if not redis_ok:
            logger.error("❌ Redis 管理器测试失败，停止测试")
            return
        
        # 2. 测试 Supabase 登录和 Session 管理
        user_id = await test_supabase_login_and_session()
        if not user_id:
            logger.error("❌ Supabase 登录测试失败，停止测试")
            return
        
        # 3. 测试 Session 清理
        await test_session_cleanup(user_id)
        
        logger.success("🎉 所有测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 清理资源
        try:
            await redis_manager.close()
            logger.info("🔒 Redis 连接已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭 Redis 连接失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
