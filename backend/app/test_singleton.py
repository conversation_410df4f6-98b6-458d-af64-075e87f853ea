#!/usr/bin/env python3
"""
测试单例模式是否正确工作
"""

import asyncio
from loguru import logger
from backend.app.db.redis_conn import RedisManager


async def test_singleton_behavior():
    """测试单例行为"""
    logger.info("🧪 开始测试单例行为...")
    
    # 创建多个实例
    manager1 = RedisManager()
    manager2 = RedisManager()
    manager3 = RedisManager()
    
    # 验证是否是同一个实例
    logger.info(f"Manager1 ID: {id(manager1)}")
    logger.info(f"Manager2 ID: {id(manager2)}")
    logger.info(f"Manager3 ID: {id(manager3)}")
    
    if manager1 is manager2 is manager3:
        logger.success("✅ 单例模式工作正常：所有实例都是同一个对象")
    else:
        logger.error("❌ 单例模式失败：创建了多个实例")
        return False
    
    # 测试初始化只执行一次
    await manager1.initialize()
    client1 = await manager1.get_client()
    
    await manager2.initialize()  # 应该不会重复初始化
    client2 = await manager2.get_client()
    
    if client1 is client2:
        logger.success("✅ 初始化只执行一次：客户端是同一个对象")
    else:
        logger.error("❌ 初始化执行了多次：客户端不是同一个对象")
        return False
    
    return True


async def test_concurrent_access():
    """测试并发访问"""
    logger.info("🧪 开始测试并发访问...")
    
    async def create_manager():
        manager = RedisManager()
        await manager.initialize()
        return id(manager)
    
    # 并发创建多个管理器
    tasks = [create_manager() for _ in range(10)]
    manager_ids = await asyncio.gather(*tasks)
    
    # 检查所有 ID 是否相同
    unique_ids = set(manager_ids)
    if len(unique_ids) == 1:
        logger.success(f"✅ 并发访问测试通过：所有管理器 ID 相同 ({list(unique_ids)[0]})")
        return True
    else:
        logger.error(f"❌ 并发访问测试失败：创建了 {len(unique_ids)} 个不同的实例")
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始单例模式测试...")
    
    try:
        # 测试基本单例行为
        singleton_ok = await test_singleton_behavior()
        if not singleton_ok:
            return
        
        # 测试并发访问
        concurrent_ok = await test_concurrent_access()
        if not concurrent_ok:
            return
        
        logger.success("🎉 所有单例测试通过！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 清理资源
        try:
            manager = RedisManager()
            await manager.close()
            logger.info("🔒 Redis 连接已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭 Redis 连接失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
