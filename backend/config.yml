# ===========================================
# FastAPI Auth System Configuration File
# Non-sensitive structured configuration
# ===========================================

# ===========================================
# Application Basic Settings
# ===========================================
app_name: "FastAPI Auth System"
app_port: 8088
app_version: "1.0.0"
app_reload: true
app_workers: 1
app_ip_location_query: true
app_same_time_login: true
app_host: "0.0.0.0"

# Initialize base data on startup
app_init_base_data_on_startup: true

# User agent list
app_user_agents:
  - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# CORS configuration
app_cors_origins:
  - "*"
app_cors_credentials: true

# HTTP client settings
app_http_timeout: 30.0
app_download_timeout: 30.0

# NAS settings
app_nas_base_path: "/app/videos"

# ===========================================
# Database Configuration (Non-sensitive)
# ===========================================
db_type: "postgresql"
db_echo: false
db_max_overflow: 10
db_pool_size: 20
db_pool_recycle: 3600
db_pool_timeout: 30
db_push_to_db: true

# ===========================================
# Redis Configuration (Non-sensitive)
# ===========================================
redis_decode_responses: true
redis_max_connections: 20
redis_socket_connect_timeout: 5
redis_socket_timeout: 5
redis_retry_on_timeout: true
redis_health_check_interval: 30

# ===========================================
# JWT Configuration (Non-sensitive)
# ===========================================
#jwt_algorithm: "HS256"
#jwt_access_token_expire_time: 30      # minutes
#jwt_refresh_token_expire_time: 10080  # minutes (7 days)

# ===========================================
# Notion Integration Configuration (Non-sensitive)
# ===========================================
notion_push_to_notion: false