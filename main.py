from contextlib import asynccontextmanager
from fastapi import FastAPI
from loguru import logger

# 导入 Redis 管理器
from backend.app.db.redis_conn import redis_manager


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化 Redis
    try:
        await redis_manager.initialize()
        logger.info("🚀 应用启动，Redis 已初始化")
    except Exception as e:
        logger.error(f"❌ Redis 初始化失败: {e}")
        # 可以选择是否继续启动应用
        # raise e  # 如果 Redis 是必需的，取消注释这行

    yield

    # 关闭时清理 Redis 连接
    try:
        await redis_manager.close()
        logger.info("🔒 应用关闭，Redis 连接已清理")
    except Exception as e:
        logger.error(f"❌ Redis 关闭失败: {e}")


# 创建 FastAPI 应用，使用生命周期管理
app = FastAPI(
    title="FastAPI Auth System",
    description="FastAPI 认证系统，集成 Supabase 和 Redis",
    version="1.0.0",
    lifespan=lifespan
)


@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.get("/hello/{name}")
async def say_hello(name: str):
    return {"message": f"Hello {name}"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查 Redis 连接
        redis_healthy = await redis_manager.health_check()

        return {
            "status": "healthy" if redis_healthy else "degraded",
            "redis": "connected" if redis_healthy else "disconnected",
            "message": "Service is running"
        }
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "redis": "error",
            "message": f"Health check failed: {str(e)}"
        }
