from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from loguru import logger

# 导入 Redis 管理器
from backend.app.db.redis_conn import redis_manager
# 导入 API 路由
from backend.app.api.session_routes import router as session_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化 Redis
    try:
        await redis_manager.initialize()
        # 将 Redis 管理器设置到应用状态中
        app.state.redis = redis_manager.get_client() # type: ignore
        logger.info("🚀 应用启动，Redis 已初始化并设置到 app.state")
    except Exception as e:
        logger.error(f"❌ Redis 初始化失败: {e}")
        app.state.redis = None # type: ignore
        # 可以选择是否继续启动应用
        # raise e  # 如果 Redis 是必需的，取消注释这行

    yield

    # 关闭时清理 Redis 连接
    try:
        if hasattr(app.state, 'redis') and app.state.redis: # type: ignore
            await app.state.redis.close() # type: ignore
            logger.info("🔒 应用关闭，Redis 连接已清理")
    except Exception as e:
        logger.error(f"❌ Redis 关闭失败: {e}")


# 创建 FastAPI 应用，使用生命周期管理
app = FastAPI(
    title="FastAPI Auth System",
    description="FastAPI 认证系统，集成 Supabase 和 Redis",
    version="1.0.0",
    lifespan=lifespan
)

# 注册路由
app.include_router(session_router)


@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.get("/hello/{name}")
async def say_hello(name: str):
    return {"message": f"Hello {name}"}


@app.get("/health")
async def health_check(request: Request):
    """健康检查端点"""
    try:
        # 优先从 app.state 获取 Redis 管理器，回退到全局单例
        redis_mgr = getattr(request.app.state, 'redis', None) or redis_manager

        if redis_mgr:
            redis_healthy = await redis_mgr.health_check()
            return {
                "status": "healthy" if redis_healthy else "degraded",
                "redis": "connected" if redis_healthy else "disconnected",
                "message": "Service is running",
                "redis_source": "app.state" if hasattr(request.app.state, 'redis') else "global"
            }
        else:
            return {
                "status": "unhealthy",
                "redis": "not_initialized",
                "message": "Redis manager not available"
            }
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "redis": "error",
            "message": f"Health check failed: {str(e)}"
        }
